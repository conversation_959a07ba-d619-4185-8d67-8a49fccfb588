from django.db import models
from RTRDA.model import BaseModel


class Maintenancework(BaseModel):
    code = models.CharField(db_column='Code', max_length=200, db_collation='Thai_CI_AI')  # Field name made lowercase.
    systemcomponent = models.Char<PERSON>ield(db_column='SystemComponent', max_length=500, db_collation='Thai_CI_AI')  # Field name made lowercase.
    maintenancetype = models.CharField(db_column='MaintenanceType', max_length=1, db_collation='Thai_CI_AI')  # Field name made lowercase.
    scheduledate = models.DateTimeField(db_column='ScheduleDate')  # Field name made lowercase.
    actualdate = models.DateTimeField(db_column='ActualDate', blank=True, null=True)  # Field name made lowercase.
    technician = models.CharField(db_column='Technician', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)  # Field name made lowercase.
    status = models.BooleanField(db_column='Status')  # Field name made lowercase.
    costestimate = models.FloatField(db_column='CostEstimate')  # Field name made lowercase.
    remark = models.TextField(db_column='Remark', db_collation='Thai_CI_AI', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'MaintenanceWork'

